"""
write the question number in comments
### Basic Concepts
1. **Variables and Data Types**
   - Q1: Assign the value 10 to a variable named `x`. Print the value of `x`.
   - Q2: Create a variable `y` and assign the string "Hello, <PERSON>!" to it. Print `y`.
   - Q3: What will be the output of `print(type(42))`? Write a code to verify your answer.

2. **Strings**
   - Q4: Assign the string "Python" to a variable `language`. Print the length of the string using `len()`.
   - Q5: Concatenate the strings "Hello" and "World" with a space in between. Print the result.
   - Q6: Write a function `first_char` that takes a string as an parameter and returns its string and print the string.

3. **Integers and Arithmetic Operations**
   - Q7: Assign the value 15 to a variable `a` and the value 7 to a variable `b`. Print the result of `a + b`.
   - Q8: Write a function `square` that takes an integer and returns its square.
   - Q9: Calculate the remainder of `45 divided by 6` using the modulo operator and print the result.

4. **Print and Input**
   - Q10: Write a code that asks the user for their name and prints a greeting message including their name.
   - Q11: Write a code to print "The value of x is 10" using a variable `x` with value 10.

### Intermediate Concepts
5. **Conditionals**
   - Q12: Write a function `is_even` that takes an integer and returns `True` if it is even, otherwise `False`.
   - Q13: Write a function `compare` that takes two integers and returns the larger one. If they are equal, return either one.
   - Q14: Write a code to check if a given string `s` contains the letter "a". Print "Yes" if it does, otherwise print "No".

6. **Functions**
   - Q15: Write a function `greet` that takes a name as an argument and prints "Hello, [name]!".
   - Q16: Write a function `sum_numbers` that takes two integers and returns their sum.
   - Q17: Write a function `absolute_difference` that takes two integers and returns the absolute difference between them.

7. **Lists and Loops**
   - Q18: Create a list `numbers` with the values [1, 2, 3, 4, 5]. Print each element using a for loop.
   - Q19: Write a function `sum_list` that takes a list of integers and returns the sum of all elements.
   - Q20: Write a function `find_max` that takes a list of integers and returns the maximum value.

8. **String Methods**
   - Q21: Write a code that converts the string "hello world" to uppercase and prints it.
   - Q22: Write a function `count_vowels` that takes a string and returns the number of vowels in it.
   - Q23: Write a function `reverse_string` that takes a string and returns it reversed.

### Advanced Concepts
9. **Nested Conditionals and Loops**
   - Q24: Write a function `classify_number` that takes an integer and returns "Positive" if it is positive, "Negative" if it is negative, and "Zero" if it is zero.
   - Q25: Write a code to print all prime numbers between 1 and 50.
   - Q26: Write a function `fizz_buzz` that takes an integer `n` and returns "Fizz" if `n` is divisible by 3, "Buzz" if `n` is divisible by 5, "FizzBuzz" if it is divisible by both, otherwise returns `n`.

10. **Lists and Functions**
    - Q27: Write a function `filter_even` that takes a list of integers and returns a new list with only the even numbers.
    - Q28: Write a function `map_double` that takes a list of integers and returns a new list with each value doubled.
    - Q29: Write a function `find_second_largest` that takes a list of integers and returns the second largest value.

11. **String Formatting**
    - Q30: Write a code to format the string "Name: [name], Age: [age]" using variables `name` and `age`.
    - Q31: Write a function `format_date` that takes three integers `day`, `month`, and `year`, and returns a formatted string "dd-mm-yyyy".
    - Q32: Write a code to format a number to two decimal places.

12. **Advanced Functions**
    - Q33: Write a function `factorial` that takes a non-negative integer and returns its factorial.
    - Q34: Write a function `fibonacci` that takes an integer `n` and returns the first `n` numbers in the Fibonacci sequence.
    - Q35: Write a function `is_palindrome` that checks if a given string is a palindrome.

13. **Advanced Conditionals**
    - Q36: Write a function `grade` that takes a score and returns a grade according to the following scheme: A for 90+, B for 80-89, C for 70-79, D for 60-69, F for below 60.
    - Q37: Write a function `leap_year` that checks if a given year is a leap year.
    - Q38: Write a function `unique_elements` that takes a list and returns a list of unique elements.

14. **Complex List Operations**
    - Q39: Write a function `rotate_list` that takes a list and an integer `k`, and returns the list rotated `k` positions to the right.
    - Q40: Write a function `flatten` that takes a list of lists and returns a single list with all the elements.
    - Q41: Write a function `merge_sorted_lists` that takes two sorted lists and returns a single sorted list by merging them.

15. **Recursive Functions**
    - Q42: Write a recursive function `sum_recursive` that takes a list of integers and returns the sum.
    - Q43: Write a recursive function `gcd` that takes two integers and returns their greatest common divisor.
    - Q44: Write a recursive function `power` that takes a base and an exponent, and returns the base raised to the power of the exponent.

16. **Lambda Functions**
    - Q45: Write a lambda function to add two numbers.
    - Q46: Write a lambda function to find the maximum of two numbers.
    - Q47: Write a lambda function to filter out all odd numbers from a list.

17. **Error Handling**
    - Q48: Write a function `safe_divide` that takes two numbers and returns their division. If the divisor is zero, return "Error: Division by zero".
    - Q49: Write a function `validate_age` that takes an age and raises a `ValueError` if the age is negative.
    - Q50: Write a function `read_file` that takes a filename and returns its content. If the file does not exist, return "Error: File not found".

These questions should help your students progress from basic to advanced Python programming concepts."""