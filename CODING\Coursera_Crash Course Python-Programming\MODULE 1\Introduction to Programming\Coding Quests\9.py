# In a passcode where each digit of the passcode is independent of the other digits and each digit can be any numeral from 0 through 9, the total number of combinations is the number of possibilities for each digit raised to the power of the length of the passcode. So, for a 1-numeral passcode, there would be 10 possibilities; one for every numeral from 0 to 9.  For a 2-numeral passcode, each numeral is independent of the other, so there would be 10 times 10 possibilities. 

# Using this information, use Python to calculate and print the number of possible passwords that can be formed with 3 numerals. 

# Note: Your result should be in the number format, not a sentence.

# Should print 1000

# Enter code here:

