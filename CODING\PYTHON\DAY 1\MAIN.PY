#PRINT FUNCTION IN PYTHON
'''
The print function in Python is used to output data to the standard output device (like a screen). It can be used to print text, variables, or the results of expressions.

Here's a breakdown of the print function:

1.print() is a built-in function in Python.
2.It can take multiple arguments, separated by commas.
3.Each argument is printed in the order it is passed to the function.
4.By default, the arguments are separated by a space character.
5.The print function automatically adds a newline character at the end of the 6.6.output, moving the cursor to the next line.
For example, print("Hello", "World") would output Hello World on the screen, with a space separating the two words. The cursor would then move to the next line.

'''

print("HELLO WORLD")
print(7)
print()