# Code Snippet 1: Basic Variable Usage with F-String
# f-strings (formatted string literals) are a way to embed expressions inside string literals, using curly braces {}.

name = "<PERSON>"
# Using f-string to include the variable 'name' within a string
greeting = f"Hello, {name}!"
print(greeting)

# Code Snippet 2: Basic Arithmetic with F-String
# f-strings can also evaluate expressions directly within the braces.

a = 5
b = 3
# Evaluating the expression a + b inside the f-string
result = f"The sum of {a} and {b} is {a + b}."
print(result)

# Code Snippet 3: Using F-String with Function Calls
# You can call functions within the braces of an f-string.

def square(x):
    return x * x

number = 4
# Calling the function square() within the f-string
squared_result = f"The square of {number} is {square(number)}."
print(squared_result)

# Code Snippet 4: Input and F-Strings
# Taking user input and using it in an f-string.

user_name = input("Enter your name: ")
# Embedding user input directly in the f-string
welcome_message = f"Welcome, {user_name}!"
print(welcome_message)

# Code Snippet 5: Conditional Expressions in F-Strings
# You can use conditional expressions directly within f-strings.

age = 20
# Using a ternary conditional within the f-string
age_status = f"You are {'an adult' if age >= 18 else 'a minor'}."
print(age_status)

# Code Snippet 6: String Methods in F-Strings
# Applying string methods within f-strings.

message = "hello world"
# Using the .upper() method within the f-string
formatted_message = f"{message.upper()}!"
print(formatted_message)

# Code Snippet 7: Nested F-Strings
# Using f-strings inside f-strings.

person_name = "Alice"
# Nesting f-strings within f-strings
sentence = f"{f'Hello, {person_name}!'} How are you?"
print(sentence)

# Code Snippet 8: Formatting Numbers in F-Strings
# Using f-strings to format numbers.

pi = 3.14159
# Formatting the number to two decimal places within the f-string
formatted_pi = f"Pi rounded to two decimal places is {pi:.2f}."
print(formatted_pi)

# Code Snippet 9: Using Multiple Variables in F-Strings
# Including multiple variables in a single f-string.

first_name = "John"
last_name = "Doe"
# Combining multiple variables within a single f-string
full_name = f"My name is {first_name} {last_name}."
print(full_name)

# Code Snippet 10: Using F-Strings with Escape Characters
# Including escape characters within f-strings.

path = "C:\\Users\\<USER>\n{second_line}"
print(multiline_message)

# Code Snippet 20: F-Strings with Conditional Expressions (Advanced)
# Using more complex conditional logic within f-strings.

score = 85
# Applying advanced conditional logic within the f-string
grade_message = f"Your grade is {'A' if score >= 90 else 'B' if score >= 80 else 'C' if score >= 70 else 'D' if score >= 60 else 'F'}."
print(grade_message)
