{"files.associations": {"*.c": "cpp", "iostream": "cpp", "algorithm": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "bitset": "cpp", "cctype": "cpp", "cfenv": "cpp", "chrono": "cpp", "cinttypes": "cpp", "clocale": "cpp", "cmath": "cpp", "codecvt": "cpp", "complex": "cpp", "condition_variable": "cpp", "csetjmp": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cuchar": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "set": "cpp", "string": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "scoped_allocator": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "thread": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "xstring": "cpp"}, "cSpell.words": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "accusantium", "<PERSON><PERSON><PERSON>", "Adars<PERSON>", "adipisicing", "amet", "androidx", "animi", "aperiam", "aptr", "arrowsize", "atque", "baseptr", "Bathroomaccessories", "<PERSON><PERSON>", "calcgmean", "cgpa", "CGPA", "CGST", "<PERSON><PERSON><PERSON>", "cofactor", "commodi", "consectetur", "consequatur", "Cubicfeet", "cum<PERSON>", "currptr", "dataframe", "debitis", "<PERSON><PERSON><PERSON>", "destinationfile", "Digram", "dolorem", "dolorum", "eigenvals", "eius", "eligendi", "elit", "enim", "excepturi", "<PERSON><PERSON>", "fibo", "figsize", "Filecopied", "firebaseapp", "firebasestorage", "FRFFKB", "gmean", "hadoo", "harum", "hasse", "<PERSON><PERSON>", "incrementer", "Incrementer", "instanceof", "Intels", "ioboiighwojhe", "isgreater", "islesser", "isyana", "<PERSON><PERSON>", "iusto", "kadane", "kalia", "kappend", "kotlinx", "Kotlinx", "ktor", "labore", "Laboriosam", "lastdigit", "Laudantium", "libx", "maxsum", "meterorpiece", "minidx", "molestiae", "mollitia", "mydata", "mydataset", "namaste", "nesciunt", "networkx", "newhead", "newstring", "newxor", "nextptr", "nobis", "nonwrapsum", "numstr", "odio", "oihargpharg<PERSON><PERSON>", "o<PERSON><PERSON><PERSON>", "optimise", "Optimised", "optio", "<PERSON><PERSON>", "perferendis", "perspiciatis", "Plumbingitems", "possimus", "pqrs", "prashu", "printinfo", "proto", "quisquam", "racecar", "<PERSON><PERSON>", "Ratione", "repellat", "reprehend<PERSON><PERSON>", "Rfgx", "salut", "SGST", "sibu", "sirname", "soluta", "squarefeet", "Squarefeet", "Steelbars", "subarray", "Subarray", "subarrays", "<PERSON><PERSON>", "submatrix", "subproblems", "subu", "succesfully", "<PERSON><PERSON><PERSON><PERSON>", "sunt", "<PERSON><PERSON><PERSON>", "tempora", "tempxor", "todelete", "totalsum", "Totam", "ullam", "unde", "<PERSON><PERSON><PERSON>", "veniam", "videofile", "vocabify", "voluptatem", "Woodenproducts", "WOXSEN", "<PERSON><PERSON><PERSON>'s", "wrapsum", "wxyz", "xlabel", "xorsum", "xpoints", "yashu", "ylabel", "ypoints", "Zendriya"], "code-runner.clearPreviousOutput": true, "code-runner.ignoreSelection": true, "code-runner.runInTerminal": true, "code-runner.saveAllFilesBeforeRun": true, "code-runner.saveFileBeforeRun": true, "java.configuration.updateBuildConfiguration": "automatic", "livePreview.defaultPreviewPath": "/HTML & CSS Full Course - Beginner to Pro/buttons.html", "zencoder.enableRepoIndexing": true}