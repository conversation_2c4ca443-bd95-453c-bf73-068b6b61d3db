# Radix Sort
'''
In today's digital age, managing and organizing large datasets of mobile phone numbers is a critical task in sectors like telecommunications, customer relationship management (CRM), and digital marketing. Traditional comparison-based sorting algorithms like Quick Sort or Merge Sort operate with a time complexity of O(n log n), which may not be optimal when dealing with large-scale numeric datasets like 10-digit mobile numbers.

Mobile number are fixed length numerics values , which makes them an ideal candidate for non-comparison based sorting algorithms . Radix sort , with its linear time complexity of O(nk),
where k is the number of digits, provides a highly efficient solution for sorting such datasets.

the primary challenge is to implement a sorting mechanism that :
- Handles large volumes of 10-digit mobile numbers efficiently.
- Maintains stability and accuracy.
- operates with optimal time and space complexity.
'''

def counting_sort_by_digit(arr, exp):
    n = len(arr)
    output = [0] * n
    count = [0] * 10  # digit 0-9

    # count the occurrences of digits at current exp position
    for i in range(n):
        index = (arr[i] // exp) % 10
        count[index] += 1

    # cumulative count
    for i in range(1, 10):
        count[i] += count[i - 1]

    # build output array (stable sort, process from end)
    for i in range(n - 1, -1, -1):
        index = (arr[i] // exp) % 10
        count[index] -= 1
        output[count[index]] = arr[i]

    # copy output to original array
    for i in range(n):
        arr[i] = output[i]


def radix_sort(arr):
    if not arr:
        return []

    max_num = max(arr)
    exp = 1
    while max_num // exp > 0:
        counting_sort_by_digit(arr, exp)
        exp *= 10

    return arr


# Test the implementation with 10 random 10-digit phone numbers
phone_numbers = [7326988411, 8886668436, 7416106782, 9918190536, 799529210,
9178648194, 9178117181, 9437339491, 7077666171, 9667788990]

print("Original Phone Numbers:", phone_numbers)
sorted_phone_numbers = radix_sort(phone_numbers.copy())
print("Sorted Phone Numbers:", sorted_phone_numbers)
