def insertion_sort(arr):
    """Sort array using insertion sort"""
    for i in range(1, len(arr)):
        key = arr[i]
        j = i - 1
        while j >= 0 and arr[j] > key:
            arr[j + 1] = arr[j]
            j -= 1
        arr[j + 1] = key
    return arr

def selection_sort(arr):
    """Sort array using selection sort"""
    for i in range(len(arr)):
        min_idx = i
        for j in range(i + 1, len(arr)):
            if arr[j] < arr[min_idx]:
                min_idx = j
        arr[i], arr[min_idx] = arr[min_idx], arr[i]
    return arr

def bubble_sort(arr):
    """Sort array using bubble sort"""
    n = len(arr)
    for i in range(n):
        for j in range(0, n - i - 1):
            if arr[j] > arr[j + 1]:
                arr[j], arr[j + 1] = arr[j + 1], arr[j]
    return arr

def merge_sort(arr):
    """Sort array using merge sort"""
    if len(arr) <= 1:
        return arr
    
    mid = len(arr) // 2
    left = merge_sort(arr[:mid])
    right = merge_sort(arr[mid:])
    
    return merge(left, right)

def merge(left, right):
    result = []
    i = j = 0
    
    while i < len(left) and j < len(right):
        if left[i] <= right[j]:
            result.append(left[i])
            i += 1
        else:
            result.append(right[j])
            j += 1
            
    result.extend(left[i:])
    result.extend(right[j:])
    return result

def quick_sort(arr):
    """Sort array using quick sort"""
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quick_sort(left) + middle + quick_sort(right)

def radix_sort(arr):
    """Sort array using radix sort"""
    RADIX = 10
    placement = 1
    
    max_digit = max(arr)
    while placement < max_digit:
        buckets = [list() for _ in range(RADIX)]
        for i in arr:
            tmp = int((i / placement) % RADIX)
            buckets[tmp].append(i)
        a = 0
        for b in range(RADIX):
            buck = buckets[b]
            for i in buck:
                arr[a] = i
                a += 1
        placement *= RADIX
    return arr

def bucket_sort(arr):
    """Sort array using bucket sort"""
    if not arr:
        return arr
    
    min_value = min(arr)
    max_value = max(arr)
    bucket_range = (max_value - min_value) / len(arr)
    
    if bucket_range == 0:
        return arr
    
    buckets = [[] for _ in range(len(arr))]
    
    for num in arr:
        bucket_index = int((num - min_value) / bucket_range)
        if bucket_index == len(arr):
            bucket_index -= 1
        buckets[bucket_index].append(num)
    
    for bucket in buckets:
        bucket.sort()
    
    result = []
    for bucket in buckets:
        result.extend(bucket)
    
    return result

# Test cases
def test_sorting_algorithms():
    # Test case 1: Random numbers
    test1 = [64, 34, 25, 12, 22, 11, 90]
    print("Test Case 1 - Random numbers:")
    print("Original:", test1)
    print("Insertion sort:", insertion_sort(test1.copy()))
    print("Selection sort:", selection_sort(test1.copy()))
    print("Bubble sort:", bubble_sort(test1.copy()))
    print("Merge sort:", merge_sort(test1.copy()))
    print("Quick sort:", quick_sort(test1.copy()))
    print("Radix sort:", radix_sort(test1.copy()))
    print("Bucket sort:", bucket_sort(test1.copy()))
    print()
    
    # Test case 2: Already sorted
    test2 = [1, 2, 3, 4, 5, 6, 7]
    print("Test Case 2 - Already sorted:")
    print("Original:", test2)
    print("Insertion sort:", insertion_sort(test2.copy()))
    print()
    
    # Test case 3: Reverse sorted
    test3 = [7, 6, 5, 4, 3, 2, 1]
    print("Test Case 3 - Reverse sorted:")
    print("Original:", test3)
    print("Insertion sort:", insertion_sort(test3.copy()))
    print()
    
    # Test case 4: Duplicate numbers
    test4 = [4, 2, 9, 6, 5, 1, 8, 3, 7, 4, 5, 6]
    print("Test Case 4 - With duplicates:")
    print("Original:", test4)
    print("Insertion sort:", insertion_sort(test4.copy()))
    print()
    
    # Test case 5: Large numbers (for radix sort)
    test5 = [170, 45, 75, 90, 802, 24, 2, 66]
    print("Test Case 5 - Large numbers (radix sort):")
    print("Original:", test5)
    print("Radix sort:", radix_sort(test5.copy()))

# Run the tests
test_sorting_algorithms()