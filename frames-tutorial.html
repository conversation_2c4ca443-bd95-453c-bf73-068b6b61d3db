<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTML Frames Tutorial</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .explanation {
            background-color: #e8f4fd;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            background-color: white;
            border-radius: 5px;
        }
        
        /* Row arrangement */
        .row-container {
            display: flex;
            height: 150px;
            gap: 10px;
        }
        
        .box1 { background-color: #ffcdd2; width: 200px; }
        .box2 { background-color: #c8e6c9; width: 300px; }
        .box3 { background-color: #fff3e0; width: 150px; }
        
        /* Column arrangement */
        .col-container {
            display: flex;
            flex-direction: column;
            width: 200px;
            gap: 10px;
        }
        
        .col-box1 { background-color: #e1bee7; height: 80px; }
        .col-box2 { background-color: #b3e5fc; height: 120px; }
        .col-box3 { background-color: #dcedc8; height: 60px; }
        
        /* Percentage-based layout */
        .percent-container {
            display: flex;
            height: 120px;
            gap: 2%;
        }
        
        .percent-box1 { background-color: #ffab91; width: 30%; }
        .percent-box2 { background-color: #a5d6a7; width: 45%; }
        .percent-box3 { background-color: #90caf9; width: 23%; }
        
        /* Grid layout */
        .grid-container {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            grid-template-rows: 100px 80px;
            gap: 10px;
            height: 200px;
        }
        
        .grid-box1 { background-color: #f8bbd9; }
        .grid-box2 { background-color: #b39ddb; }
        .grid-box3 { background-color: #81c784; }
        .grid-box4 { background-color: #ffcc02; }
        .grid-box5 { background-color: #ff8a65; }
        .grid-box6 { background-color: #4fc3f7; }
        
        /* Mixed units */
        .mixed-container {
            display: flex;
            height: 100px;
            gap: 10px;
        }
        
        .mixed-box1 { background-color: #ce93d8; width: 150px; }
        .mixed-box2 { background-color: #80deea; flex: 1; }
        .mixed-box3 { background-color: #c5e1a5; width: 25%; }
        
        .frame-box {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: bold;
            border: 2px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>HTML Frames Tutorial</h1>
    
    <div class="explanation">
        <h2>What are Frames?</h2>
        <p><strong>Frames</strong> are rectangular containers that divide a webpage into sections. In modern web development, we use CSS Flexbox and Grid instead of the deprecated HTML frameset. Frames help organize content into structured layouts.</p>
    </div>

    <!-- Row Arrangement -->
    <div class="demo-section">
        <h3>1. Frames in a Row (Fixed Widths)</h3>
        <p>Three frames arranged horizontally with fixed pixel widths:</p>
        <div class="row-container">
            <div class="frame-box box1">Frame 1<br>200px</div>
            <div class="frame-box box2">Frame 2<br>300px</div>
            <div class="frame-box box3">Frame 3<br>150px</div>
        </div>
    </div>

    <!-- Column Arrangement -->
    <div class="demo-section">
        <h3>2. Frames in a Column (Fixed Heights)</h3>
        <p>Three frames stacked vertically with fixed pixel heights:</p>
        <div class="col-container">
            <div class="frame-box col-box1">Frame A<br>80px</div>
            <div class="frame-box col-box2">Frame B<br>120px</div>
            <div class="frame-box col-box3">Frame C<br>60px</div>
        </div>
    </div>

    <!-- Percentage Layout -->
    <div class="demo-section">
        <h3>3. Percentage-Based Layout</h3>
        <p>Frames using percentage widths (responsive):</p>
        <div class="percent-container">
            <div class="frame-box percent-box1">30%</div>
            <div class="frame-box percent-box2">45%</div>
            <div class="frame-box percent-box3">23%</div>
        </div>
    </div>

    <!-- Grid Layout -->
    <div class="demo-section">
        <h3>4. Grid Layout (2 Rows × 3 Columns)</h3>
        <p>Six frames arranged in a grid pattern:</p>
        <div class="grid-container">
            <div class="frame-box grid-box1">Grid 1</div>
            <div class="frame-box grid-box2">Grid 2</div>
            <div class="frame-box grid-box3">Grid 3</div>
            <div class="frame-box grid-box4">Grid 4</div>
            <div class="frame-box grid-box5">Grid 5</div>
            <div class="frame-box grid-box6">Grid 6</div>
        </div>
    </div>

    <!-- Mixed Units -->
    <div class="demo-section">
        <h3>5. Mixed Units Layout</h3>
        <p>Combining fixed pixels, percentages, and flexible units:</p>
        <div class="mixed-container">
            <div class="frame-box mixed-box1">150px</div>
            <div class="frame-box mixed-box2">Flexible (flex: 1)</div>
            <div class="frame-box mixed-box3">25%</div>
        </div>
    </div>

    <div class="explanation">
        <h3>Key Concepts:</h3>
        <ul>
            <li><strong>Fixed Units (px):</strong> Exact pixel measurements</li>
            <li><strong>Percentages (%):</strong> Relative to parent container</li>
            <li><strong>Flexible (flex):</strong> Takes remaining space</li>
            <li><strong>Grid:</strong> Two-dimensional layout system</li>
            <li><strong>Flexbox:</strong> One-dimensional layout system</li>
        </ul>
    </div>
</body>
</html>
