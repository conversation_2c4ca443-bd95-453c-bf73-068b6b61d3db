'''

1. **Temperature Conversion Program**: Write a function `convert_temperature()` that takes an input temperature in Fahrenheit from the user, converts it to Celsius using the formula `C = (F - 32) * 5/9`, and returns the converted temperature. Use conditional statements to check if the input is valid (i.e., a number) and handle invalid inputs gracefully. Use f-strings to format the output.

2. **String Manipulation and Analysis**: Write a function `analyze_string()` that takes a string input from the user, and then:
   - Converts the string to lowercase.
   - Counts the number of vowels and consonants in the string.
   - Finds the index of the first occurrence of a specific character (given as an argument).
   - Returns a formatted string with all this information using f-strings.

3. **Basic Calculator**: Write a function `basic_calculator()` that takes two numbers and an operator (+, -, *, /) from the user, performs the corresponding operation, and returns the result. Use input validation to ensure the numbers and operator are valid. If the operator is not valid, print an appropriate message and ask for input again. Use typecasting where necessary.

4. **List Filtering with Loops**: Write a function `filter_even_numbers()` that takes a list of numbers as an argument, uses a for loop to iterate through the list, and returns a new list containing only the even numbers. Use the `break` and `continue` statements where necessary.

5. **Password Validation**: Write a function `validate_password()` that takes a password input from the user and checks if it meets the following criteria:
   - At least 8 characters long
   - Contains both uppercase and lowercase letters
   - Contains at least one digit
   - Contains at least one special character (e.g., @, #, $, etc.)
   
   Use string methods, loops, and conditionals to implement the validation. Return a message indicating whether the password is valid or not.

6. **Word Frequency Counter**: Write a function `word_frequency()` that takes a string input from the user, splits the string into words, and returns a dictionary where the keys are the words and the values are the number of times each word appears in the string. Use string methods and loops for this task.

7. **Simple Banking System**: Write a function `banking_system()` that simulates a simple banking system. The function should:
   - Take an initial balance as an argument.
   - Allow the user to make deposits and withdrawals.
   - Check for sufficient balance before allowing a withdrawal.
   - Use a while loop to continue the operations until the user decides to quit.
   
   Use functions, loops, conditionals, and input validation to implement the system. Return the final balance at the end.
'''


