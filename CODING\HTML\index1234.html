<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Case Shop</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background-color: #f7f7f7;
        }
        header {
            background: linear-gradient(90deg, #ff7e5f, #feb47b);
            color: white;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        nav {
            display: flex;
            justify-content: center;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        nav a {
            padding: 15px 20px;
            text-decoration: none;
            color: #333;
            font-weight: bold;
        }
        nav a:hover {
            background-color: #ff7e5f;
            color: white;
        }
        .container {
            padding: 20px;
        }
        .product {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 20px;
            text-align: center;
        }
        .product img {
            width: 100%;
            height: auto;
            transition: transform 0.2s;
        }
        .product img:hover {
            transform: scale(1.05);
        }
        .product-info {
            padding: 15px;
        }
        .product-info h2 {
            margin: 10px 0;
            font-size: 1.5em;
            color: #333;
        }
        .product-info p {
            color: #666;
            margin: 10px 0;
        }
        .product-info button {
            background: linear-gradient(90deg, #ff7e5f, #feb47b);
            border: none;
            color: white;
            padding: 10px 20px;
            font-size: 1em;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .product-info button:hover {
            background: linear-gradient(90deg, #feb47b, #ff7e5f);
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 15px 0;
        }
        @media (max-width: 768px) {
            header h1 {
                font-size: 2em;
            }
            .product-info h2 {
                font-size: 1.25em;
            }
            .product-info p {
                font-size: 0.9em;
            }
            .product-info button {
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Phone Case Shop</h1>
    </header>
    <nav>
        <a href="#">Home</a>
        <a href="#">Products</a>
        <a href="#">About</a>
        <a href="#">Contact</a>
    </nav>
    <div class="container">
        <div class="grid">
            <div class="product">
                <img src="https://via.placeholder.com/300" alt="Phone Case 1">
                <div class="product-info">
                    <h2>Stylish Phone Case</h2>
                    <p>$19.99</p>
                    <button>Add to Cart</button>
                </div>
            </div>
            <div class="product">
                <img src="https://via.placeholder.com/300" alt="Phone Case 2">
                <div class="product-info">
                    <h2>Elegant Phone Case</h2>
                    <p>$24.99</p>
                    <button>Add to Cart</button>
                </div>
            </div>
            <div class="product">
                <img src="https://via.placeholder.com/300" alt="Phone Case 3">
                <div class="product-info">
                    <h2>Classic Phone Case</h2>
                    <p>$29.99</p>
                    <button>Add to Cart</button>
                </div>
            </div>
        </div>
    </div>
    <footer>
        <p>&copy; 2024 Phone Case Shop. All rights reserved.</p>
    </footer>
</body>
</html>
