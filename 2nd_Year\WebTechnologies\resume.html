<style>

.logo{
    text-align: center;
    font-family: Verdana, Geneva, Tahoma, sans-serif;
    font-size: xx-large;
    font-weight: bolder;
    text-decoration: underline;
    margin: 0%;
    padding: 0%;
    
}

.top-navbar{
    background-color: rgba(0, 0, 0, 0.706);
    justify-content: center;
    align-items: center;
    justify-items: center;
    height: 50px;
    text-align: center;
}

.anavli{
    text-decoration: none;
    color: orange;
    /* padding: 10%; */
    padding-right: 10%;
    padding-left: 10%;
}

.topnavli{
    text-align: center;
    padding-top: 10px;
    width: 100%;
    font-size:x-large;
    font-weight: bold;
    font-family: Arial, Helvetica, sans-serif;
}

.hero-section{
    width: 100%;
    height: 1080px;
}

.profile{
 background-color: rgba(255, 236, 170, 0.736);
     /*
    height: 300px;
    width: 300px;
    border-radius: 150px;
    margin: 50px; */
    justify-content: center;
    justify-items: center;
    justify-self: center;
    width: 100%;
    height: 60%;
}

.userimg{
    justify-content: center;
    justify-items: center;
    justify-self: center;
    width: 20%; 
    border-radius: 250px; 
    /* padding: 20%; */
    padding-top: 5%;
    /* padding-left: 50%; */
    /* padding-right: 50%; */
    margin-left:42%;
    padding-bottom: 0%;
    
}

.username{
    /* text-align: center; */
    font-size: xx-large;
    font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
    margin-left: 6%;
}

.edu{
        /* justify-content: left;
    justify-self: left; */
   /* justify-content: right;
   justify-items:right; */
   /* justify-self:; */
   background-color: rgb(194, 151, 255);
   /* width: 30%; */
   height: 50%;
   border-radius: 50px;
   /* -ms-flex-align: end; */
   /* margin-left: 50%; */
   /* margin-top: 0px; */
   /* margin-bottom: 150%; */
   margin: 5%;
}

.projects{

    /* justify-content: right;
    justify-self: right; */
    /* align-items: ; */
    /* justify-items: right; */
    
    background-color: rgb(148, 234, 255);
    /* width: 30%; */
    height: 50%;

    border-radius: 50px;
    margin: 5%;
    
}

.both{
    width: 100%;
}

</style>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Resume</title>
</head>
    <body>
        
        
        <h2 class="logo">AAJ</h2>
        <div class="top-navbar">

            <ol class="topnavli">
                <a class="anavli" href="#">Home</a>
                <a class="anavli" href="#">Project</a>
                <a class="anavli" href="#">About Me</a>
            </ol>

        </div>

        <div class="hero-section">

            <div class="profile">
                <img class="userimg" src="aajg.png" alt="#">
                <p class="username">A.Adarsh Jagannath</p>
                <p style="width: 75%; text-align: center;">Lorem ipsum dolor sit amet consectetur adipisicing elit. In similique fugiat consequatur labore sit optio consequuntur voluptates excepturi et, exercitationem cum odit doloribus voluptatibus tempore. Cum quisquam adipisci et ea.</p>
                <p style="width: 75%; text-align: center;">Lorem ipsum dolor sit amet consectetur adipisicing elit. In similique fugiat consequatur labore sit optio consequuntur voluptates excepturi et, exercitationem cum odit doloribus voluptatibus tempore. Cum quisquam adipisci et ea.</p>
                <p style="width: 75%; text-align: center;">Lorem ipsum dolor sit amet consectetur adipisicing elit. In similique fugiat consequatur labore sit optio consequuntur voluptates excepturi et, exercitationem cum odit doloribus voluptatibus tempore. Cum quisquam adipisci et ea.</p>
            </div>

            <div class="both" style="flex-direction: row; flex: content; align-items:center; ">

            <div class="both">

            <div class="both" style="display: flex;">

            <div class="edu">
                <h1 style="text-align: center; font-family: Verdana, Geneva, Tahoma, sans-serif;">Education</h1>
                <p style="font-size: xx-large;">Woxsen University</p>
                <p style="font-size: large; font-weight: bold; margin-left: 5%;">B.Tech Computer Science</p>
                <p style="text-align: center; padding: 3%;">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Neque similique facere repellat aut? Velit cum amet illum dolorem culpa! Non et repellat repudiandae aliquid deleniti labore ad temporibus praesentium vitae, ea earum facere quia. Consectetur, minus nemo! Officiis assumenda atque odit porro tenetur aliquam voluptas deserunt, laborum minima ex debitis eaque autem omnis quas dolorum dolore tempora maxime molestias aut! Illo ullam temporibus, ex eligendi rerum maxime praesentium deleniti animi natus sit voluptas quo nemo! Repellat voluptatem eligendi aperiam harum a, deserunt ipsum! Similique odio dolores repellendus possimus sunt mollitia, vero, sed quas natus minus deserunt ratione saepe. Cumque, voluptas.
                </p>
                <p style="text-align: center; padding: 3%;">
                    Lorem ipsum dolor sit amet consectetur adipisicing elit. Sit velit inventore id possimus qui laudantium, vel eveniet? Voluptate omnis iure eveniet aut unde, aliquam ex? Consectetur exercitationem, aliquid ad veritatis enim modi similique quae! Laudantium aperiam placeat repellendus reprehenderit id nam necessitatibus et optio! Voluptatum incidunt blanditiis neque provident fugiat!
                </p>
            </div>

            <div class="projects">

                <h1 style="text-align: center; align-items: center;">Projects</h1>

                <ul style="font-size: large;">
                    <li style="font-weight: bolder; font-family: Georgia, 'Times New Roman', Times, serif; text-decoration: underline;">Vocabify</li>
                    <p style="margin-left: 5%;">Lorem ipsum dolor sit amet consectetur adipisicing elit. Mollitia laborum animi rem, facere voluptatem dolores labore nostrum soluta suscipit at, sint, consectetur aliquid in exercitationem. Eaque, et dolorum cupiditate error voluptatibus, voluptates praesentium magnam ullam dolor, blanditiis ut repellendus? Itaque voluptatibus debitis a odit quam! Fugit delectus odit architecto quasi!</p>
                    
                    <li style="font-weight: bolder; font-family: Georgia, 'Times New Roman', Times, serif; text-decoration: underline;">SyncSphere</li>
                    <p style="margin-left: 5%;">Lorem ipsum dolor sit amet consectetur adipisicing elit. Mollitia laborum animi rem, facere voluptatem dolores labore nostrum soluta suscipit at, sint, consectetur aliquid in exercitationem. Eaque, et dolorum cupiditate error voluptatibus, voluptates praesentium magnam ullam dolor, blanditiis ut repellendus? Itaque voluptatibus debitis a odit quam! Fugit delectus odit architecto quasi!</p>
                    
                    <li style="font-weight: bolder; font-family: Georgia, 'Times New Roman', Times, serif; text-decoration: underline;">Nutrify</li>
                    <p style="margin-left: 5%;">Lorem ipsum dolor sit amet consectetur adipisicing elit. Mollitia laborum animi rem, facere voluptatem dolores labore nostrum soluta suscipit at, sint, consectetur aliquid in exercitationem. Eaque, et dolorum cupiditate error voluptatibus, voluptates praesentium magnam ullam dolor, blanditiis ut repellendus? Itaque voluptatibus debitis a odit quam! Fugit delectus odit architecto quasi!</p>

                    <li style="font-weight: bolder; font-family: Georgia, 'Times New Roman', Times, serif; text-decoration: underline;">Empath-AI</li>
                    <p style="margin-left: 5%;">Lorem ipsum dolor sit amet consectetur adipisicing elit. Mollitia laborum animi rem, facere voluptatem dolores labore nostrum soluta suscipit at, sint, consectetur aliquid in exercitationem. Eaque, et dolorum cupiditate error voluptatibus, voluptates praesentium magnam ullam dolor, blanditiis ut repellendus? Itaque voluptatibus debitis a odit quam! Fugit delectus odit architecto quasi!</p>
                </ul>

            </div>

            </div>

        </div>
        
    </body>
</html>