<style>
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    min-height: 100vh;
}

/* Header Section */
.header {
    text-align: center;
    padding: 40px 0;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 40px;
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
    border: 4px solid #007bff;
}

.name {
    font-size: 2.5rem;
    font-weight: 300;
    margin-bottom: 10px;
    color: #2c3e50;
}

.title {
    font-size: 1.2rem;
    color: #6c757d;
    margin-bottom: 20px;
}

.contact-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.contact-item {
    color: #007bff;
    text-decoration: none;
    font-size: 0.9rem;
}

.contact-item:hover {
    text-decoration: underline;
}

/* Main Content */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-top: 40px;
}

.section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
}

/* Education Section */
.education-item {
    margin-bottom: 25px;
}

.degree {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.university {
    font-size: 1rem;
    color: #007bff;
    margin-bottom: 5px;
}

.year {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.description {
    font-size: 0.9rem;
    color: #555;
    line-height: 1.5;
}

/* Projects Section */
.project-item {
    margin-bottom: 25px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #007bff;
}

.project-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.project-description {
    font-size: 0.9rem;
    color: #555;
    line-height: 1.5;
}

/* About Section */
.about-text {
    font-size: 0.95rem;
    color: #555;
    line-height: 1.6;
    text-align: justify;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 0;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .name {
        font-size: 2rem;
    }

    .contact-info {
        gap: 15px;
        flex-direction: column;
        align-items: center;
    }

    .profile-img {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 20px 0;
    }

    .name {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.3rem;
    }

    .project-item {
        padding: 15px;
    }
}
</style>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.Adarsh Jagannath - Resume</title>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <img class="profile-img" src="aajg.png" alt="A.Adarsh Jagannath">
            <h1 class="name">A.Adarsh Jagannath</h1>
            <p class="title">Computer Science Student</p>
            <div class="contact-info">
                <a href="mailto:<EMAIL>" class="contact-item"><EMAIL></a>
                <a href="tel:+91-XXXXXXXXXX" class="contact-item">+91-XXXXXXXXXX</a>
                <a href="https://linkedin.com/in/adarsh-jagannath" class="contact-item">LinkedIn</a>
                <a href="https://github.com/AdarshJ173" class="contact-item">GitHub</a>
            </div>
        </header>

        <!-- About Section -->
        <section class="section">
            <h2 class="section-title">About Me</h2>
            <p class="about-text">
                Passionate Computer Science student with a strong foundation in software development and emerging technologies.
                Experienced in building innovative applications and solving complex problems through code.
                Always eager to learn new technologies and contribute to meaningful projects that make a positive impact.
            </p>
        </section>

        <!-- Main Content Grid -->
        <div class="main-content">
            <!-- Education Section -->
            <section class="section">
                <h2 class="section-title">Education</h2>
                <div class="education-item">
                    <div class="degree">B.Tech Computer Science</div>
                    <div class="university">Woxsen University</div>
                    <div class="year">2022 - 2026 (Expected)</div>
                    <p class="description">
                        Currently pursuing Bachelor of Technology in Computer Science with focus on software engineering,
                        data structures, algorithms, and modern development practices. Maintaining strong academic performance
                        while actively participating in coding competitions and technical projects.
                    </p>
                </div>
            </section>

            <!-- Projects Section -->
            <section class="section">
                <h2 class="section-title">Projects</h2>

                <div class="project-item">
                    <div class="project-name">Vocabify</div>
                    <p class="project-description">
                        A vocabulary building application designed to help users expand their word knowledge through
                        interactive learning methods. Features include spaced repetition, progress tracking, and
                        personalized learning paths.
                    </p>
                </div>

                <div class="project-item">
                    <div class="project-name">SyncSphere</div>
                    <p class="project-description">
                        A collaborative platform that enables real-time synchronization and sharing of resources
                        across multiple devices. Built with modern web technologies to ensure seamless user experience
                        and data consistency.
                    </p>
                </div>

                <div class="project-item">
                    <div class="project-name">Nutrify</div>
                    <p class="project-description">
                        A nutrition tracking application that helps users monitor their dietary intake and maintain
                        healthy eating habits. Features include meal planning, calorie counting, and nutritional analysis.
                    </p>
                </div>

                <div class="project-item">
                    <div class="project-name">Empath-AI</div>
                    <p class="project-description">
                        An AI-powered emotional intelligence application that analyzes user interactions and provides
                        insights for better communication and emotional understanding. Utilizes machine learning
                        algorithms for sentiment analysis.
                    </p>
                </div>
            </section>
        </div>
    </div>
</body>
</html>