'''
1. Write a Python program to calculate the area of a rectangle using variables.

2. Create a string of your favorite fruits and print the second fruit.

3. Write a function that takes a person's name as input and prints a greeting.

4. Use an f-string to print your name and age in a sentence.

5. Create a string and print its length using a built-in function.

6. Write a program that checks if a number is even or odd.

7. Create a list of numbers and use a for loop to print each number multiplied by 2.

8. Write a while loop that counts down from 10 to 1.

9. Create a program that asks the user for their age and prints if they are old enough to vote.

10. Write a function that takes two numbers as arguments and returns their sum.

11. Use the `split()` method to separate a sentence into a list of words.

12. Write a program that uses `break` to exit a loop when a specific condition is met.

13. 1a a loop that skips printing even numbers using `continue`.

14. Write a program that converts a temperature from Celsius to Fahrenheit.-_-

15. Create a list of numbers and use list slicing to print every other number.-_-

16. Write a function that returns the reverse of a given string.-_-

17. Use the `input()` function to ask the user for their favorite color and print it back to them.

18. Create a program that checks if a year is a leap year.-_-

19. Write a function that takes a list of numbers and returns the sum of all even numbers.-_-

20. Use string formatting to print a person's name and age.

21. Create a program that generates a simple multiplication table for a given number.

22. Write a function that counts the number of vowels in a given string.

23. Use a while loop to implement a simple guessing game.

24. Create a program that removes all spaces from a given string.

25. Write a function that takes a number as an argument and returns its factorial.

26. Use the `in` operator to check if an item exists in a list.

27. Create a program that capitalizes the first letter of each word in a sentence.

28. Write a function that returns the largest number in a list.

29. Use string methods to check if a given string is a palindrome.

30. Create a program that converts a number to its corresponding month name.

31. Write a function that takes a string and returns a dictionary of character frequencies.

32. Use list comprehension to create a list of squares of numbers from 1 to 10.

33. Create a program that simulates a simple calculator using functions and conditionals.

34. Write a function that checks if a number is prime.

35. Use the `range()` function to create a list of odd numbers between 1 and 20.

36. Create a program that finds the second-largest number in a list.

37. Write a function that takes a sentence and returns the longest word.

38. Use string methods to remove any punctuation from a given string.

39. Create a program that prints the Fibonacci sequence up to a given number.

40. Write a function that takes a list of strings and returns them sorted by length.

41. Use a nested loop to create a simple pattern of asterisks.

42. Create a program that converts a given number of days to years, months, and days.

43. Write a function that takes a string and returns it with words in reverse order.

44. Use the `zip()` function to combine two lists into a dictionary.

45. Create a program that checks if a given number is a perfect number.

46. Write a function that takes a list of numbers and returns a new list with duplicates removed.

47. Use string methods to validate if a given string is a valid email address.

48. Create a program that generates a random password of a specified length.

49. Write a function that takes a sentence and returns the number of words in it.

50. Use list methods to implement a simple to-do list program with add and remove functionality.
'''