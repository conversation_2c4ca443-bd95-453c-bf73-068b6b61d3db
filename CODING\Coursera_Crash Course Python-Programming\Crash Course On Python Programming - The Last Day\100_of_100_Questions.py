'''

Variables and Data Types:
1. Declare a variable named 'age' and assign it the value 25.
2. Create a variable to store your name as a string.
3. Declare a variable to hold the value of pi (3.14159).
4. Create a boolean variable named 'is_student' and set it to True.
5. Declare a variable to store a list of your favorite colors.
Strings and String Methods:
6. Create a string variable with your full name and print its length.
7. Convert the string "Hello, World!" to uppercase.
8. Extract the first three characters from the string "Python".
9. Replace all occurrences of 'a' with '@' in the string "banana".
10. Check if the string "Python programming" starts with "Python".
F-strings:
11. Use an f-string to print your name and age.
12. Create an f-string that calculates and prints the area of a rectangle.
13. Use an f-string to format a float number to two decimal places.
14. Create an f-string that includes a mathematical expression.
15. Use an f-string to print a dictionary's keys and values.
Indexing:
16. Print the last character of the string "Python" using negative indexing.
17. Access the second element of the list [1, 2, 3, 4, 5].
18. Print the first and last elements of the tuple (10, 20, 30, 40, 50).
19. Create a string and print its middle character.
20. Access the value for the key 'name' in the dictionary {'name': '<PERSON>', 'age': 30}.

Input and Typecasting:
21. Ask the user for their age and convert it to an integer.
22. Get two numbers from the user and print their sum.
23. Ask the user for their height in meters and convert it to a float.
24. Get a string input from the user and convert it to lowercase.
25. Ask the user for a boolean value (True/False) and convert the input to a boolean.

Operators:
26. Calculate the remainder when 17 is divided by 3.
27. Use the exponentiation operator to calculate 2 to the power of 5.
28. Check if 10 is greater than 5 and 20 is less than 30 using logical operators.
29. Use the 'in' operator to check if 'a' is in the string "Hello".
30. Perform floor division of 17 by 3.

Conditionals (if, else, elif):
31. Write a program to check if a number is positive, negative, or zero.
32. Create a simple calculator that performs addition, subtraction, multiplication, or division based on user input.
33. Write a program to determine if a year is a leap year.
34. Create a program that gives a grade (A, B, C, D, or F) based on a numerical score.
35. Write a program to find the maximum of three numbers.

Loops (for and while):
36. Print the first 10 natural numbers using a for loop.
37. Calculate the sum of numbers from 1 to 100 using a while loop.
38. Print the multiplication table of a given number.
39. Find the factorial of a number using a for loop.
40. Print all even numbers between 1 and 20 using a while loop.

Break and Continue:
41. Use a break statement to exit a loop when it encounters a specific number.
42. Use continue to skip printing multiples of 3 in a range from 1 to 20.
43. Create a program that keeps asking for input until the user types 'quit'.
44. Print numbers from 1 to 50, but skip multiples of 7 using continue.
45. Create a loop that breaks when it finds the first prime number after 20.

Functions and Function Arguments:
46. Write a function to calculate the area of a circle given its radius.
47. Create a function that takes a name as an argument and prints a greeting.
48. Write a function to check if a number is even or odd.
49. Create a function that returns the maximum of two numbers.
50. Write a function that takes a variable number of arguments and returns their sum.

Lists:
51. Create a list of numbers and find its length.
52. Add an element to the end of a list.
53. Remove a specific element from a list.
54. Sort a list of numbers in descending order.
55. Create a new list containing only the even numbers from an existing list.

Tuples:
56. Create a tuple with three elements and print them.
57. Try to modify an element of a tuple and handle the error.
58. Unpack a tuple into individual variables.
59. Find the index of a specific element in a tuple.
60. Convert a tuple to a list and back to a tuple.

Dictionaries:
61. Create a dictionary to store information about a person (name, age, city).
62. Add a new key-value pair to an existing dictionary.
63. Remove a key-value pair from a dictionary.
64. Check if a key exists in a dictionary.
65. Print all keys and values of a dictionary separately.

Mixed Concepts - Basic:
66. Create a function that takes a list of numbers and returns the average.
67. Write a program that counts the frequency of each character in a string.
68. Create a list of tuples, each containing a name and an age, then sort the list based on age.
69. Write a function that takes a sentence and returns the longest word.
70. Create a program that simulates a simple bank account (deposit, withdraw, check balance).

Mixed Concepts - Intermediate:
71. Write a program to find all prime numbers up to a given number using a function.
72. Create a function that checks if a given word is a palindrome.
73. Write a program that removes all duplicates from a list while preserving the original order.
74. Create a function that takes a dictionary and returns a new dictionary with keys and values swapped.
75. Write a program that simulates a simple game of hangman.

Mixed Concepts - Advanced:
76. Implement a basic calculator that can handle multiple operations in a single expression (e.g., "2 + 3 * 4").
77. Create a function that generates the Fibonacci sequence up to n terms using a generator.
78. Write a program that reads a CSV file and performs basic analysis (e.g., average, max, min) on numerical columns.
79. Implement a simple text-based adventure game using functions and dictionaries.
80. Create a program that simulates a deck of cards, with functions to shuffle and deal.

Mixed Concepts - More Advanced:
81. Implement a basic version of the game "Tic Tac Toe" for two players.
82. Create a function that solves a quadratic equation and returns the roots.
83. Write a program that implements a simple Caesar cipher for encrypting and decrypting messages.
84. Create a function that calculates the distance between two points in 2D space.
85. Implement a basic stack data structure with push, pop, and peek operations using a list.

Mixed Concepts - Challenging:
86. Create a program that generates a maze and solves it using a simple algorithm.
87. Implement a basic version of the game "Connect Four" for two players.
88. Write a program that simulates a simple vending machine with products, prices, and coin handling.
89. Create a function that performs matrix multiplication for two 2D lists.
90. Implement a basic spell checker that suggests corrections for misspelled words.

Mixed Concepts - Very Challenging:
91. Create a program that simulates a simple database with CRUD operations using dictionaries.
92. Implement a basic version of the game "Minesweeper".
93. Write a program that performs basic image processing operations (e.g., rotate, flip) on a 2D list representing pixel values.
94. Create a function that solves Sudoku puzzles.
95. Implement a simple compression algorithm for strings (e.g., run-length encoding).

Mixed Concepts - Expert:
96. Create a program that simulates a simple neural network for basic pattern recognition.
97. Implement a basic version of the game "Chess" with move validation and basic AI.
98. Write a program that performs basic natural language processing tasks (e.g., tokenization, sentiment analysis) on a given text.
99. Create a function that generates a fractal image (e.g., Mandelbrot set) as a 2D list of pixel values.
100. Implement a simple interpreter for a custom programming language with basic arithmetic and control structures.

'''