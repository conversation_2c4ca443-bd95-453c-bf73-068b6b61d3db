# Understanding For Loops

# Basic Idea:
# A for loop allows you to iterate over a sequence of elements, executing a block of code for each element.

# Syntax:
# for variable in sequence:
#     # Code block to execute

# The loop assigns each element in the sequence to the variable and then executes the code block.

# Basic Example: Iterating over a range of numbers
for i in range(5):  # range(5) generates numbers from 0 to 4
    print(i)
# Explanation:
# The loop iterates over the numbers from 0 to 4 (generated by range(5)) and prints each number.

print('\n')

for i in range(5,10):  # range(5) generates numbers from 0 to 4
    print(i)

print("\n")

# Intermediate Example: Iterating with a step value+
for i in range(0, 10, 2):  # range(start, stop, step) generates numbers from 0 to 9 with a step of 2
    print(i)
# Explanation:
# The loop iterates over the numbers from 0 to 8 (0, 2, 4, 6, 8) and prints each number.

# Using For Loop with Functions
def square(n):
    return n * n

for i in range(5):  # Iterate over numbers from 0 to 4
    print(f"The square of {i} is {square(i)}")
# Explanation:
# The function square(n) returns the square of n.
# The loop iterates over the numbers from 0 to 4, calls the square function for each number, and prints the result.

# For Loop with Input and Conditionals
# Taking user input and using a for loop to check if the number is prime
num = int(input("Enter a number: "))  # Get user input and convert to integer

if num > 1:  # Check if the number is greater than 1
    for i in range(2, num):
        if (num % i) == 0:
            print(f"{num} is not a prime number")
            break  # Exit the loop if a divisor is found
    else:
        print(f"{num} is a prime number")
else:
    print(f"{num} is not a prime number")
# Explanation:
# The user inputs a number.
# The loop iterates from 2 to num-1 and checks if the number is divisible by any of these values.
# If a divisor is found, the loop prints that the number is not a prime and breaks out of the loop.
# If no divisors are found, it prints that the number is a prime.

# Advanced Example: Nested For Loops
# Using nested for loops to create a multiplication table
for i in range(1, 6):  # Outer loop for rows
    for j in range(1, 6):  # Inner loop for columns
        print(f"{i * j}\t", end='')  # Print the product followed by a tab
    print()  # Move to the next line after each row
# Explanation:
# The outer loop iterates over the numbers from 1 to 5, representing the rows.
# The inner loop iterates over the numbers from 1 to 5, representing the columns.
# The product of the current row and column values is printed, followed by a tab for formatting.
# After each row is completed, a newline is printed to move to the next row.

# --------------------------------------------------------------------------------------------------------------------

# range(start, stop, step)
# start: The value where the sequence begins. The default is 0 if not specified.

# stop: The value where the sequence ends, but this value is not included in the sequence. The loop will iterate until it reaches this value.

# step: The increment (or decrement) between each value in the sequence. The default is 1 if not specified.

# Example Usage in a for Loop

for i in range(1, 8, 2):
    print(i)
# start=1: The sequence starts at 1.
# stop=8: The sequence ends before 8 (so it goes up to 7).
# step=2: The values increase by 2 each time (1, 3, 5, 7).

# Key Concepts:
# 1. Iteration: Repeating a process for each element in a sequence.
# 2. Sequence: An ordered collection of items (e.g., range of numbers).
# 3. Variable: Used to store each element in the sequence during iteration.
# 4. Function: A block of code that performs a specific task.
# 5. Conditionals: Used to make decisions based on conditions.
# 6. Input: Getting user input to interact with the program.
