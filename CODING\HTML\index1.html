<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
    </head>

    <body>
        <h1 style="background-color: aqua;">This is My 1st Heading</h1>
        <h2 style="color:crimson;">This is My 2nd Heading</h2>
        <h3>This is My 3rd Heading</h3>
        <h4>This is My 4th Heading</h4>
        <h5>This is My 5th Heading</h5>
        <h6>This is My 6th Heading</h6>

        <h7>This is My 7th Heading</h7>

        <p>This is My 1st Paragraph</p>

        <a href="https://www.google.com">Click Here</a>

        <!-- unordered lists -->
        <ul>
            <li>maggi</li>
            <li>kurkure</li>
            <li>lays</li>
        </ul>

        <!-- ordered lists -->
        <ol>
            <li>heat the water</li>
            <li>blend it with the coffee</li>
            <li>add milk</li>
        </ol>

        <table>
            <tr>
                <td>sr.no</td>
                <td>name</td>
                <td>login</td>
            </tr>
            <tr>
                <td>1</td>
                <td>2</td>
                <td>3</td>
            </tr>
            <tr>
                <td>4</td>
                <td>5</td>
                <td>6</td>
            </tr>
        </table>

        <table>
            <tr>
                <td>A</td>
                <td>B</td>
                <td>C</td>
            </tr>
            <tr>
                <td>1</td>
                <td>2</td>
                <td>3</td>
            </tr>
            <tr>
                <td>!</td>
                <td>@</td>
                <td>#</td>
            </tr>
        </table>

        <img src="https://www.google.com/imgres?q=4k%20images&imgurl=https%3A%2F%2Fimages.pexels.com%2Fphotos%2F842711%2Fpexels-photo-842711.jpeg%3Fcs%3Dsrgb%26dl%3Dpexels-christian-heitz-285904-842711.jpg%26fm%3Djpg&imgrefurl=https%3A%2F%2Fwww.pexels.com%2Fsearch%2F4k%2F&docid=0U_4UJmLU0zi2M&tbnid=NVbTzINb46-HbM&vet=12ahUKEwjh39KhjriGAxXsUGwGHZ-bAfQQM3oECB0QAA..i&w=5472&h=3648&hcb=2&ved=2ahUKEwjh39KhjriGAxXsUGwGHZ-bAfQQM3oECB0QAA" alt="4k Image">
        <!-- @ Understanding HTML Attributes

**I. Introduction**

HTML stands for HyperText Markup Language. It's the foundation for creating web pages. HTML elements (like headings, paragraphs, images) can have additional instructions attached to them using attributes. These attributes provide more information about how the element should behave or be displayed.

**II. Analogy: Building with Instructions and Labels**

Imagine building a Lego model. The Lego bricks are like HTML elements.  The basic instructions tell you how to put the bricks together.  Attributes are like little labels on the bricks that give extra information.  For example, a red brick might have a label "stop sign" to indicate its purpose in the model.

**III. How Attributes Work**

* Attributes come in name/value pairs, written within the opening tag of an HTML element.
* The format is `<element_name attribute_name="attribute_value">`.
* Example: `<img src="image.jpg" alt="A beautiful sunset">`

**IV. Common Attributes**

* **`src` (source):** Used with the `<img>` tag to specify the path to an image file.
* **`href` (hypertext reference):** Used with the `<a>` tag to define the link a user clicks on.
* **`alt` (alternative text):** Provides alternative text for images, important for accessibility.
* **`id`:** Assigns a unique identifier to an element, useful for styling or scripting.
* **`class`:** Assigns a class name to an element, allowing you to apply styles to groups of elements.
* **`style`:** Used to define inline styles for an element (generally discouraged in favor of separate stylesheets).

**V. Why Use Attributes?**

* **Provide Additional Information:** Attributes give more context to the HTML element, telling the browser how to handle it.
* **Enhance Accessibility:** Attributes like `alt` text help make web pages accessible to users with disabilities.
* **Add Interactivity:**  Attributes like `href` allow you to create clickable links.

**VI. First Principles Thinking:  Clear Instructions**

* When building something, clear instructions are essential to ensure correct assembly.
* HTML attributes act like extra instructions for the web browser. They provide specific details about how to display or handle an HTML element.
* By using attributes effectively, you can create web pages that are functional, accessible, and visually appealing.

**VII. Summary**

HTML attributes are essential for adding functionality and meaning to your web pages. They provide a way to customize the behavior and appearance of HTML elements, resulting in a more user-friendly and informative web experience. -->

        <p><b>Lorem</b> <strong>ipsum</strong> dolor sit amet consectetur adipisicing elit. Reiciendis autem ipsa provident omnis <em>adipisci</em>. Blanditiis omnis voluptas, qui <i>nobis</i> voluptatibus <u>quidem</u> eius laboriosam nisi <s>impedit</s> rem! Accusamus ullam odio consectetur.
        </p>

        <p>
            2<sup>5</sup>
        </p>
        <p>
            O<sub>2</sub>
        </p>

        <p> this is an example <br/> of break</p>

        <pre>
            My name is AAJ & I am going to BE & DO what i want.
            I will.
            Do it.
        </pre>

        <!-- emmet -->

        


    </body>
</html>