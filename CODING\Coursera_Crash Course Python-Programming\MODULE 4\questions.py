'''

10. Write a function that takes two numbers as arguments and returns their sum.

11. Use the `split()` method to separate a sentence into a list of words.

12. Write a program that uses `break` to exit a loop when a specific condition is met.

13. Create a loop that skips printing even numbers using `continue`.

17. Use the `input()` function to ask the user for their favorite color and print it back to them.

20. Use string formatting to print a person's name and age.

22. Write a function that counts the number of vowels in a given string.

24. Create a program that removes all spaces from a given string.

26. Use the `in` operator to check if an item exists in a list.

27. Create a program that capitalizes the first letter of each word in a sentence.

'''

UR_COLOR = input("enter ur fav color: " )
print("your fav color is: " + UR_COLOR)

