''' VARIABLES AND DATA TYPES'''

# variables are like containers - they store some values in the form of numeric or string or anything 

a = 6784 
print(a)


b = "Radhika"
print(b)

c = True
print(c)

d = None
print(d)

a1 = 3857
b1 = "Merchant"

c1 = False
d1 = None

# data type is a type of value that a variable can hold in it , used to perform operations without errors 

# concatination - to add variable of same data type
print(type(a))
print(type(b))
print(type(c))
print(type(d))

# ------------------------------------------------------

print( a + a1)
print( b + b1)
# print( c + c1)
# print( d + d1)


# -------------------------------------------------------------------------------------------------------------

'''
numeric data : int, float, complex
text data  : string ( str ) : "AAJ"
boolean data : True or False
sequence data : list , tuple
mapped data : dictionary (dict)

'''
# EVERYTHING IS AN OBJECT IN PYTHON