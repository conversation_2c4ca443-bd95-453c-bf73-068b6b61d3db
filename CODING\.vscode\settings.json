{"zenMode.centerLayout": false, "zenMode.hideActivityBar": true, "zenMode.hideLineNumbers": false, "zenMode.hideStatusBar": true, "markdown-preview-enhanced.enablePreviewZenMode": true, "code-runner.clearPreviousOutput": true, "editor.wordWrap": "on", "editor.wordWrapColumn": 80, "notebook.output.wordWrap": true, "chat.editor.wordWrap": "on", "background.autoInstall": true, "background.renderContentAboveBackground": true, "background.smoothImageRendering": true, "cSpell.words": ["AAJMRK", "<PERSON><PERSON><PERSON>", "Aarnavy", "aarnvy", "a<PERSON>n", "<PERSON><PERSON><PERSON>", "Adars<PERSON>", "adipisicing", "<PERSON><PERSON><PERSON>", "amet", "anshuman", "asynclive", "autoexit", "badi", "bala<PERSON><PERSON><PERSON><PERSON>", "bhai", "bili", "blablabla", "blazingly", "bolti", "calcgmean", "choti", "concatination", "consectetur", "deepgram", "Deepgram", "DEEPGRAM", "dotenv", "DUNIA", "<PERSON><PERSON><PERSON>", "edcba", "endpointing", "exponentiated", "ffplay", "fifthary", "fifthsize", "firstbool", "firstdbl", "firstflt", "fourthary", "gmean", "Goihawroifgfh", "groq", "Groq", "GROQ", "<PERSON><PERSON>", "hadoo", "HGWOHGOIH", "hoga", "inorder", "Inquote", "isgreater", "islesser", "JAAT", "Jadiye", "jawab", "<PERSON><PERSON>u", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "keepalive", "Khali", "kurkure", "kuta", "langchain", "Legos", "logn", "MADAR", "maggi", "<PERSON><PERSON>", "mixtral", "mults", "myname", "Namaste", "nextnum", "<PERSON><PERSON>", "nodisp", "openai", "OPENAI", "oprtn", "pathfinding", "prdct", "QWER", "RANZ", "Reconceptualizes", "sahi", "<PERSON><PERSON>", "sibu", "sortedness", "stratswith", "<PERSON><PERSON>", "subu", "tere", "thirdary", "thirdsize", "Timsort", "Trngl", "ttfb", "TTFB", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "yehi", "<PERSON><PERSON><PERSON>", "<PERSON>ckerberg"], "files.associations": {"*.c": "cpp", "ostream": "cpp", "iostream": "cpp", "vector": "cpp", "cmath": "cpp", "array": "cpp", "atomic": "cpp", "*.tcc": "cpp", "cctype": "cpp", "clocale": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "list": "cpp", "unordered_map": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "map": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "random": "cpp", "set": "cpp", "string": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "sstream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp"}, "diffEditor.wordWrap": "on", "code-runner.runInTerminal": true, "code-runner.saveFileBeforeRun": true, "C_Cpp_Runner.cCompilerPath": "gcc", "C_Cpp_Runner.cppCompilerPath": "g++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "", "C_Cpp_Runner.cppStandard": "", "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/VR_NR/Community/VC/Auxiliary/Build/vcvarsall.bat", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false, "cSpell.ignoreWords": ["Arnav", "<PERSON><PERSON><PERSON>", "TNOC", "adbsdb", "docstrings", "incrmnt"]}