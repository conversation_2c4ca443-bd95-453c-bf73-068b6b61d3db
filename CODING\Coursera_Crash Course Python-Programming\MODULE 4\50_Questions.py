'''

1. Write a program that asks the user for their name and age, then prints a greeting using an f-string.

2. Create a list of your favorite foods and print the second and fourth items.

3. Write a function that takes two numbers as arguments and returns their sum.

4. Use a for loop to print the first 10 even numbers.

5. Create a while loop that counts down from 20 to 1, skipping multiples of 3.

6. Write a program that checks if a number is positive, negative, or zero using if-elif-else statements.

7. Create a dictionary of book titles and their authors, then print all the authors.

8. Write a function that takes a string and returns it reversed.

9. Use the `split()` method to separate a sentence into a list of words, then join them back with dashes.

10. Create a program that converts temperature from Celsius to Fahrenheit using input and typecasting.

11. Write a for loop that iterates through a string and counts the number of vowels.

12. Create a tuple of months and use indexing to print the summer months.

13. Write a function that takes a list of numbers and returns the largest number.

14. Use a while loop and the `break` statement to create a simple number guessing game.

15. Create a program that removes all spaces from a given string using a string method.

16. Write a function that takes a number as an argument and returns its factorial.

17. Use list comprehension to create a list of squares of numbers from 1 to 10.

18. Create a dictionary of student names and their scores, then print the name of the student with the highest score.

19. Write a program that checks if a given year is a leap year.

20. Use the `continue` statement in a loop to print all numbers from 1 to 20, except multiples of 3.

21. Create a function that takes a string and returns a dictionary of character frequencies.

22. Write a program that simulates a simple calculator using functions and conditionals.

23. Use string formatting to print a person's name, age, and city in a sentence.

24. Create a list of numbers and use slicing to print every third number.

25. Write a function that checks if a number is prime.

26. Use a nested loop to create a simple multiplication table.

27. Create a program that finds the second-largest number in a list.

28. Write a function that takes a sentence and returns the longest word.

29. Use the `in` operator to check if a specific key exists in a dictionary.

30. Create a program that generates a simple pattern of asterisks using loops.

31. Write a function that takes a list of strings and returns them sorted by length.

32. Use string methods to validate if a given string is a valid email address.

33. Create a program that converts a given number of days to years, months, and days.

34. Write a function that takes a string and returns it with words in reverse order.

35. Use the `zip()` function to combine two lists into a dictionary.

36. Create a program that checks if a given number is a perfect number.

37. Write a function that takes a list of numbers and returns a new list with duplicates removed.

38. Use a dictionary to create a simple word-frequency counter for a given text.

39. Create a program that generates a random password of a specified length.

40. Write a function that takes a sentence and returns the number of words in it.

41. Use list methods to implement a simple to-do list program with add and remove functionality.

42. Create a program that finds all numbers between 1 and 100 that are divisible by both 3 and 5.

43. Write a function that takes a string and checks if it's a palindrome.

44. Use a loop to create a list of the first 10 Fibonacci numbers.

45. Create a dictionary of countries and their capitals, then allow the user to quiz themselves.

46. Write a program that simulates rolling a die 1000 times and prints the frequency of each number.

47. Use string methods to create a simple cipher that replaces vowels with numbers.

48. Create a function that takes a list of words and returns the longest word that is a palindrome.

49. Write a program that reads a CSV file and prints its contents as a list of dictionaries.

50. Use a combination of loops and conditionals to create a simple rock-paper-scissors game against the computer.

'''